package com.gw.agent.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.agent.constant.SensitiveWordConstant;
import com.gw.agent.dto.SensitiveWordExportDTO;
import com.gw.agent.dto.SensitiveWordQueryDTO;
import com.gw.agent.entity.SensitiveWordEntity;
import com.gw.agent.mapper.sql.SensitiveWordMapper;
import com.gw.agent.service.SensitiveWordService;
import com.gw.agent.vo.SensitiveWordImportResultVO;
import com.gw.common.exception.BusinessException;
import com.gw.common.user.context.UserContextUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 敏感词服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class SensitiveWordServiceImpl implements SensitiveWordService {
    
    private final SensitiveWordMapper sensitiveWordMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void insert(SensitiveWordEntity entity) {
        log.info("开始创建敏感词: {}", entity.getWord());
        
        // 检查敏感词是否已存在
        if (sensitiveWordMapper.findByWord(entity.getWord()).isPresent()) {
            throw new BusinessException("敏感词已存在");
        }
        
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        sensitiveWordMapper.insert(entity);
        
        log.info("敏感词创建成功，ID: {}", entity.getId());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void update(SensitiveWordEntity entity) {
        log.info("开始更新敏感词: {}", entity.getId());
        
        SensitiveWordEntity existing = findById(entity.getId());
        if (existing == null) {
            throw new BusinessException("敏感词不存在");
        }
        
        // 如果修改了词内容，检查新词是否已存在
        if (!existing.getWord().equals(entity.getWord())) {
            if (sensitiveWordMapper.findByWord(entity.getWord()).isPresent()) {
                throw new BusinessException("敏感词已存在");
            }
        }
        
        entity.setUpdateTime(LocalDateTime.now());
        sensitiveWordMapper.updateById(entity);
        
        log.info("敏感词更新成功，ID: {}", entity.getId());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void delete(Long id) {
        log.info("开始删除敏感词: {}", id);
        
        SensitiveWordEntity entity = findById(id);
        if (entity == null) {
            throw new BusinessException("敏感词不存在");
        }
        
        sensitiveWordMapper.deleteById(id);
        log.info("敏感词删除成功，ID: {}", id);
    }
    
    @Override
    public SensitiveWordEntity findById(Long id) {
        return sensitiveWordMapper.findById(id).orElse(null);
    }
    
    @Override
    public SensitiveWordEntity findByWord(String word) {
        return sensitiveWordMapper.findByWord(word).orElse(null);
    }
    
    @Override
    @Cacheable(value = SensitiveWordConstant.SENSITIVE_WORD_CACHE, key = "'enabled'")
    public List<SensitiveWordEntity> findAllEnabled() {
        return sensitiveWordMapper.findAllEnabled();
    }
    
    @Override
    @Cacheable(value = SensitiveWordConstant.SENSITIVE_WORD_CACHE, key = "'all'")
    public List<SensitiveWordEntity> findAll() {
        return sensitiveWordMapper.findAll();
    }
    
    @Override
    public List<SensitiveWordEntity> findByCategoryId(Long categoryId) {
        return sensitiveWordMapper.findByCategoryId(categoryId);
    }
    
    @Override
    public PageInfo<SensitiveWordEntity> page(int pageNum, int pageSize, SensitiveWordQueryDTO query) {
        PageHelper.startPage(pageNum, pageSize);
        List<SensitiveWordEntity> list = sensitiveWordMapper.page(query);
        return new PageInfo<>(list);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUseCount(Long id, Integer increment) {
        sensitiveWordMapper.updateUseCount(id, increment);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void batchImport(List<SensitiveWordEntity> words) {
        if (CollectionUtils.isEmpty(words)) {
            return;
        }
        
        log.info("开始批量导入敏感词，数量: {}", words.size());
        
        for (SensitiveWordEntity word : words) {
            try {
                // 检查是否已存在
                if (sensitiveWordMapper.findByWord(word.getWord()).isEmpty()) {
                    word.setCreateTime(LocalDateTime.now());
                    word.setUpdateTime(LocalDateTime.now());
                    sensitiveWordMapper.insert(word);
                }
            } catch (Exception e) {
                log.warn("导入敏感词失败: {}, 错误: {}", word.getWord(), e.getMessage());
            }
        }
        
        log.info("批量导入敏感词完成");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void batchDelete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        
        log.info("开始批量删除敏感词，数量: {}", ids.size());
        sensitiveWordMapper.deleteBatchIds(ids);
        log.info("批量删除敏感词完成");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void updateStatus(Long id, Integer status) {
        log.info("开始更新敏感词状态: {}, 状态: {}", id, status);
        
        SensitiveWordEntity entity = findById(id);
        if (entity == null) {
            throw new BusinessException("敏感词不存在");
        }
        
        entity.setStatus(status);
        entity.setUpdateTime(LocalDateTime.now());
        sensitiveWordMapper.updateById(entity);
        
        log.info("敏感词状态更新成功，ID: {}", id);
    }
    
    @Override
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public void clearCache() {
        log.info("清除敏感词缓存");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {SensitiveWordConstant.SENSITIVE_WORD_CACHE, SensitiveWordConstant.SENSITIVE_WORD_PAGE_CACHE}, allEntries = true)
    public SensitiveWordImportResultVO importFromFile(MultipartFile file, Long categoryId, Integer level,
                                                      Integer action, String replacement, Integer status, Boolean overwrite) {
        log.info("开始从文件导入敏感词，分类ID: {}", categoryId);

        SensitiveWordImportResultVO result = new SensitiveWordImportResultVO();
        result.setSkippedWords(new ArrayList<>());
        result.setFailedWords(new ArrayList<>());

        if (file == null || file.isEmpty()) {
            throw new BusinessException("文件不能为空");
        }

        String username = UserContextUtil.getCurrentUsername();
        List<String> lines = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (StringUtils.hasText(line)) {
                    lines.add(line);
                }
            }
        } catch (IOException e) {
            log.error("读取文件失败: {}", e.getMessage(), e);
            throw new BusinessException("读取文件失败: " + e.getMessage());
        }

        result.setTotalLines(lines.size());
        int successCount = 0;
        int skipCount = 0;
        int failCount = 0;

        for (String word : lines) {
            try {
                // 检查敏感词是否已存在
                SensitiveWordEntity existing = findByWord(word);
                if (existing != null) {
                    if (!overwrite) {
                        result.getSkippedWords().add(word);
                        skipCount++;
                        continue;
                    } else {
                        // 覆盖模式：更新现有敏感词
                        existing.setCategoryId(categoryId);
                        existing.setLevel(level);
                        existing.setAction(action);
                        existing.setReplacement(replacement);
                        existing.setStatus(status);
                        existing.setUpdater(username);
                        existing.setUpdateTime(LocalDateTime.now());
                        sensitiveWordMapper.updateById(existing);
                        successCount++;
                        continue;
                    }
                }

                // 创建新的敏感词
                SensitiveWordEntity entity = new SensitiveWordEntity();
                entity.setWord(word);
                entity.setCategoryId(categoryId);
                entity.setLevel(level);
                entity.setAction(action);
                entity.setReplacement(replacement);
                entity.setStatus(status);
                entity.setCreator(username);
                entity.setUpdater(username);
                entity.setCreateTime(LocalDateTime.now());
                entity.setUpdateTime(LocalDateTime.now());

                sensitiveWordMapper.insert(entity);
                successCount++;

            } catch (Exception e) {
                log.warn("导入敏感词失败: {}, 错误: {}", word, e.getMessage());
                result.getFailedWords().add(word);
                failCount++;
            }
        }

        result.setSuccessCount(successCount);
        result.setSkipCount(skipCount);
        result.setFailCount(failCount);
        result.setMessage(String.format("导入完成：成功 %d 个，跳过 %d 个，失败 %d 个", successCount, skipCount, failCount));

        log.info("敏感词导入完成，总数: {}, 成功: {}, 跳过: {}, 失败: {}", lines.size(), successCount, skipCount, failCount);
        return result;
    }

    @Override
    public byte[] exportToFile(SensitiveWordExportDTO exportDTO) {
        log.info("开始导出敏感词到文件");

        List<SensitiveWordEntity> words = findForExport(exportDTO);

        StringBuilder content = new StringBuilder();

        if (exportDTO.getFormat() == 2) {
            // 包含详细信息的格式
            content.append("# 敏感词导出文件\n");
            content.append("# 导出时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
            content.append("# 总数量: ").append(words.size()).append("\n");
            content.append("# 格式: 敏感词|分类ID|级别|动作|替换内容|状态\n");
            content.append("\n");

            for (SensitiveWordEntity word : words) {
                content.append(word.getWord())
                       .append("|").append(word.getCategoryId() != null ? word.getCategoryId() : "")
                       .append("|").append(word.getLevel() != null ? word.getLevel() : "")
                       .append("|").append(word.getAction() != null ? word.getAction() : "")
                       .append("|").append(word.getReplacement() != null ? word.getReplacement() : "")
                       .append("|").append(word.getStatus() != null ? word.getStatus() : "")
                       .append("\n");
            }
        } else {
            // 仅敏感词格式
            for (SensitiveWordEntity word : words) {
                content.append(word.getWord()).append("\n");
            }
        }

        log.info("敏感词导出完成，数量: {}", words.size());
        return content.toString().getBytes(StandardCharsets.UTF_8);
    }

    @Override
    public List<SensitiveWordEntity> findForExport(SensitiveWordExportDTO exportDTO) {
        SensitiveWordQueryDTO query = new SensitiveWordQueryDTO();

        // 设置查询条件
        if (!CollectionUtils.isEmpty(exportDTO.getCategoryIds())) {
            // 这里需要在Mapper中添加支持多个分类ID的查询方法
            // 暂时使用第一个分类ID
            query.setCategoryId(exportDTO.getCategoryIds().get(0));
        }

        if (!CollectionUtils.isEmpty(exportDTO.getLevels())) {
            query.setLevel(exportDTO.getLevels().get(0));
        }

        if (!CollectionUtils.isEmpty(exportDTO.getActions())) {
            query.setAction(exportDTO.getActions().get(0));
        }

        if (exportDTO.getStatus() != null) {
            query.setStatus(exportDTO.getStatus());
        }

        // 使用现有的page方法，但设置一个很大的页面大小来获取所有数据
        PageHelper.startPage(1, Integer.MAX_VALUE);
        List<SensitiveWordEntity> list = sensitiveWordMapper.page(query);
        return list;
    }
}
