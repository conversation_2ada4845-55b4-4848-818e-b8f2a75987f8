package com.gw.agent.controller;


import com.gw.agent.dto.SensitiveWordCategorySubmitDTO;

import com.gw.agent.entity.SensitiveWordCategoryEntity;
import com.gw.agent.service.SensitiveWordCategoryService;
import com.gw.agent.vo.SensitiveWordCategoryVO;
import com.gw.common.dto.ItemIdDTO;
import com.gw.common.dto.ResponseResult;
import com.gw.common.user.context.UserContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;


/**
 * 敏感词分类控制器
 */
@RestController
@RequestMapping("/api/v1/sensitive-word-category")
@RequiredArgsConstructor
@Tag(name = "敏感词分类管理", description = "敏感词分类相关API")
@Log4j2
public class SensitiveWordCategoryController {
    
    private final SensitiveWordCategoryService categoryService;
    
    /**
     * 创建敏感词分类
     */
    @Operation(summary = "创建敏感词分类", description = "创建一个新的敏感词分类")
    @PostMapping("")
    public ResponseResult<?> createCategory(@RequestBody @Valid SensitiveWordCategorySubmitDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        
        SensitiveWordCategoryEntity entity = new SensitiveWordCategoryEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setCreator(username);
        entity.setUpdater(username);
        
        categoryService.insert(entity);
        return ResponseResult.success(null);
    }
    
    /**
     * 更新敏感词分类
     */
    @Operation(summary = "更新敏感词分类", description = "更新敏感词分类信息")
    @PostMapping("/update")
    public ResponseResult<?> updateCategory(@RequestBody @Valid SensitiveWordCategoryUpdateDTO req) {
        String username = UserContextUtil.getCurrentUsername();

        SensitiveWordCategoryEntity entity = categoryService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词分类不存在");
        }

        BeanUtils.copyProperties(req, entity);
        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());

        categoryService.update(entity);
        return ResponseResult.success(null);
    }
    
    /**
     * 删除敏感词分类
     */
    @Operation(summary = "删除敏感词分类", description = "删除指定的敏感词分类")
    @PostMapping("/delete")
    public ResponseResult<?> deleteCategory(@RequestBody @Valid ItemIdDTO req) {
        SensitiveWordCategoryEntity entity = categoryService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词分类不存在");
        }

        categoryService.delete(req.getId());
        return ResponseResult.success(null);
    }
    
    /**
     * 获取敏感词分类详情
     */
    @Operation(summary = "获取敏感词分类详情", description = "获取敏感词分类详细信息")
    @PostMapping("/get")
    public ResponseResult<SensitiveWordCategoryVO> getCategory(@RequestBody @Valid ItemIdDTO req) {
        SensitiveWordCategoryEntity entity = categoryService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词分类不存在");
        }
        
        return ResponseResult.success(new SensitiveWordCategoryVO(entity));
    }
    
    /**
     * 获取所有敏感词分类
     */
    @Operation(summary = "获取所有敏感词分类", description = "获取所有敏感词分类列表")
    @GetMapping("/list")
    public ResponseResult<List<SensitiveWordCategoryVO>> listCategories() {
        List<SensitiveWordCategoryEntity> entities = categoryService.findAll();
        List<SensitiveWordCategoryVO> vos = entities.stream()
            .map(SensitiveWordCategoryVO::new)
            .collect(Collectors.toList());
        
        return ResponseResult.success(vos);
    }
    
    /**
     * 获取所有启用的敏感词分类
     */
    @Operation(summary = "获取所有启用的敏感词分类", description = "获取所有启用状态的敏感词分类列表")
    @GetMapping("/list/enabled")
    public ResponseResult<List<SensitiveWordCategoryVO>> listEnabledCategories() {
        List<SensitiveWordCategoryEntity> entities = categoryService.findAllEnabled();
        List<SensitiveWordCategoryVO> vos = entities.stream()
            .map(SensitiveWordCategoryVO::new)
            .collect(Collectors.toList());
        
        return ResponseResult.success(vos);
    }
    
    /**
     * 启用/禁用敏感词分类
     */
    @Operation(summary = "启用/禁用敏感词分类", description = "更新敏感词分类的启用状态")
    @PostMapping("/status/update")
    public ResponseResult<?> updateCategoryStatus(@RequestBody @Valid SensitiveWordCategoryStatusUpdateDTO req) {
        SensitiveWordCategoryEntity entity = categoryService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词分类不存在");
        }

        categoryService.updateStatus(req.getId(), req.getStatus());
        return ResponseResult.success(null);
    }
    
    /**
     * 清除敏感词分类缓存
     */
    @Operation(summary = "清除敏感词分类缓存", description = "清除敏感词分类相关缓存")
    @PostMapping("/cache/clear")
    public ResponseResult<?> clearCache() {
        categoryService.clearCache();
        return ResponseResult.success(null);
    }
}
