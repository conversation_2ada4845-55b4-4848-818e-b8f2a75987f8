package com.gw.agent.vo;

import com.gw.agent.entity.SensitiveWordEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

/**
 * 敏感词视图对象
 */
@Data
@Schema(description = "敏感词VO")
@AllArgsConstructor
@NoArgsConstructor
public class SensitiveWordVO {
    
    @Schema(description = "敏感词ID")
    private Long id;
    
    @Schema(description = "敏感词内容")
    private String word;
    
    @Schema(description = "分类ID")
    private Long categoryId;
    
    @Schema(description = "分类名称")
    private String categoryName;
    
    @Schema(description = "敏感词级别：1-低级，2-中级，3-高级，4-严重")
    private Integer level;
    
    @Schema(description = "处理动作：1-拦截，2-替换，3-警告")
    private Integer action = 1;
    
    @Schema(description = "替换内容")
    private String replacement;
    
    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;
    
    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "使用次数")
    private Integer useCount;
    
    @Schema(description = "创建者")
    private String creator;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    
    public SensitiveWordVO(SensitiveWordEntity entity) {
        BeanUtils.copyProperties(entity, this);
    }
}
